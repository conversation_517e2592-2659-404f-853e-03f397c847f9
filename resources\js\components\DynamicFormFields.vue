<template>
	<div>
		<div class="mb-3" v-for="(fieldItem, index) in formFields" :key="index">
			<CCol :xs="fieldItem.column_width">
				<div v-if="fieldItem.type === 'MULTISELECT'">
					<label class="mb-1">
						{{ fieldItem.label }}
						<span v-if="fieldItem.validation" class="text-danger">*</span>
					</label>
					<Field 
						:name="fieldItem.name"
						v-slot="{ field }"
					>
						<Multiselect
							v-bind="field"
							:mode="fieldItem.multiple ? 'tags' : 'single'"
							:value="fieldItem.value"
							:model-value="formData[fieldItem.name]"
							@update:model-value="updateFormData(fieldItem.name, $event)"
							:placeholder="fieldItem.placeholder"
							:close-on-select="false"
							:searchable="true"
							:disabled="fieldItem.disabled"
							:options="fieldItem.options"
							:can-clear="fieldItem.validation ? false : true"
						/>
					</Field>
					<ErrorMessage
						as="div"
						:name="fieldItem.name"
						class="text-danger"
					/>
				</div>
				<div v-else-if="fieldItem.type === 'USER'">
					<label class="mb-1">
						{{ fieldItem.label }}
						<span v-if="fieldItem.validation" class="text-danger">*</span>
					</label>
					<Field 
						:name="fieldItem.name"
						v-slot="{ field }"
					>
						<Multiselect
							v-bind="field"
							:mode="fieldItem.multiple ? 'tags' : 'single'"
							:value="fieldItem.value"
							:model-value="formData[fieldItem.name]"
							@update:model-value="updateFormData(fieldItem.name, $event)"
							:placeholder="fieldItem.placeholder"
							:close-on-select="false"
							:filter-results="false"
							:resolve-on-load="false"
							:infinite="true"
							:limit="10"
							:clear-on-search="true"
							:searchable="true"
							:delay="0"
							:min-chars="0"
							:object="true"
							:disabled="fieldItem.disabled"
							:options="async (query) => {
								return await getOptionUsers(query)
							}"
							:can-clear="fieldItem.validation ? false : true"
							@open="getOptionUsers('')"
						/>
					</Field>
					<ErrorMessage
						as="div"
						:name="fieldItem.name"
						class="text-danger"
					/>
				</div>
				<div v-else-if="fieldItem.type === 'DEPARTMENT'">
					<label class="mb-1">
						{{ fieldItem.label }}
						<span v-if="fieldItem.validation" class="text-danger">*</span>
					</label>
					<Field 
						:name="fieldItem.name"
						v-slot="{ field }"
					>
						<Multiselect
							v-bind="field"
							:mode="fieldItem.multiple ? 'tags' : 'single'"
							:value="fieldItem.value"
							:model-value="formData[fieldItem.name]"
							@update:model-value="updateFormData(fieldItem.name, $event)"
							:placeholder="fieldItem.placeholder"
							:close-on-select="false"
							:searchable="true"
							:object="true"
							:disabled="fieldItem.disabled"
							:options="selectOptionDepartments"
							:can-clear="fieldItem.validation ? false : true"
						>
							<template v-slot:option="{ option }">
								<div class="custom-option">
									<div class="option-label mb-1">
										{{ option.label }}
									</div>
									<div class="option-description text-secondary">
										<small>
											<i>{{ option.type }}</i>
										</small>
									</div>
								</div>
							</template>
						</Multiselect>
					</Field>
					<ErrorMessage
						as="div"
						:name="fieldItem.name"
						class="text-danger"
					/>
				</div>
				<div v-else-if="fieldItem.type === 'FILEUPLOAD'">
					<label class="mb-1">
						{{ fieldItem.label }}
						<span v-if="fieldItem.validation" class="text-danger">*</span>
					</label>
					<Field 
						:name="fieldItem.name"
						:model-value="formData[fieldItem.name]"
					>
						<FilePond
							:files="formData[fieldItem.name]"
							@updatefiles="(fileItemUploads) => updateFiles(fileItemUploads, fieldItem.name)"
							className="file-pond"
							:labelIdle="$t('validate_field.file_upload.label_idle')"
							:allowMultiple="fieldItem.multiple"
							:maxFiles="maxFiles"
							:maxFileSize="maxFileSize"
							:acceptedFileTypes="acceptedFileTypes"
							:labelFileTypeNotAllowed="$t('validate_field.file_upload.label_allowed')"
							:labelMaxFileSizeExceeded="$t('validate_field.file_upload.label_max_file_size_exceeded')"
							:fileValidateTypeLabelExpectedTypes="`${$t('validate_field.file_upload.label_expected_types')}`"
							:labelMaxFileSize="`${$t('validate_field.file_upload.label_max_file_size')} {filesize}`"
							:instantUpload="false"
							:name="fieldItem.name"
							:ref="fieldItem.name"
							credits="false"
							allow-reorder="true"
							item-insert-location="after"
							image-preview-min-height="60"
							image-preview-max-height="60"
						/>
					</Field>
					<ErrorMessage
						as="div"
						:name="fieldItem.name"
						class="text-danger"
					/>
				</div>
				<div v-else-if="fieldItem.type === 'FORMULA'">
					<FormKit
						type="text"
						:label="fieldItem.label"
						:floating-label="fieldItem.floatingLabel"
						:name="fieldItem.name"
						:model-value="formattedFormulaResults[fieldItem.name]"
						disabled="false"
						label-class="required-label"
					/>
				</div>
				<div v-else-if="fieldItem.type === 'TABLE'">
					<label class="mb-1">
						{{ fieldItem.label }}
					</label>
					<table class="table table-borderless table-job-add">
						<thead>
							<tr>
								<th v-for="(field_children, fieldIndex) in converFormFields(fieldItem.childrens, true)" :key="fieldIndex">
									{{ field_children.label }}
									<span v-if="field_children.validation" class="text-danger">*</span>
								</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="(itemChildren, itemIndex) in formattedFormulaChildrenResults(fieldItem.name)" :key="itemIndex">
								<td 
									v-for="(field_children, fieldIndex) in converFormFields(fieldItem.childrens, true)" 
									:key="fieldIndex" 
									:class="['col', `col-md-${field_children.column_width}`, `column-width-td`]"
								>
									<div v-if="field_children.type === 'MULTISELECT'">
										<Field 
											:name="`itemChildrens[${itemIndex}].${field_children.name}`"
											v-slot="{ field }"
										>
											<Multiselect
												v-bind="field"
												:mode="field_children.multiple ? 'tags' : 'single'"
												:model-value="itemChildren[field_children.name]"
												@update:model-value="updateItemChildrens(fieldItem.name, itemIndex, field_children.name, $event)"
												:placeholder="field_children.placeholder"
												:close-on-select="false"
												:searchable="true"
												:disabled="field_children.disabled"
												:options="field_children.options"
												:can-clear="field_children.validation ? false : true"
											/>
										</Field>
										<ErrorMessage
											as="div"
											:name="`itemChildrens[${itemIndex}].${field_children.name}`"
											class="text-danger"
										/>
									</div>
									<div v-else-if="field_children.type === 'USER'">
										<Field 
											:name="`itemChildrens[${itemIndex}].${field_children.name}`"
											v-slot="{ field }"
										>
											<Multiselect
												v-bind="field"
												:mode="field_children.multiple ? 'tags' : 'single'"
												:model-value="itemChildren[field_children.name]"
												@update:model-value="updateItemChildrens(fieldItem.name, itemIndex, field_children.name, $event)"
												:placeholder="field_children.placeholder"
												:close-on-select="false"
												:filter-results="false"
												:resolve-on-load="false"
												:infinite="true"
												:limit="10"
												:clear-on-search="true"
												:searchable="true"
												:delay="0"
												:min-chars="0"
												:object="true"
												:disabled="field_children.disabled"
												:options="async (query) => {
													return await getOptionUsers(query)
												}"
												:can-clear="field_children.validation ? false : true"
												@open="getOptionUsers('')"
											/>
										</Field>
										<ErrorMessage
											as="div"
											:name="`itemChildrens[${itemIndex}].${field_children.name}`"
											class="text-danger"
										/>
									</div>
									<div v-else-if="field_children.type === 'DEPARTMENT'">
										<Field
											:name="`itemChildrens[${itemIndex}].${field_children.name}`"
											v-slot="{ field }"
										>
											<Multiselect
												v-bind="field"
												:mode="field_children.multiple ? 'tags' : 'single'"
												:model-value="itemChildren[field_children.name]"
												@update:model-value="updateItemChildrens(fieldItem.name, itemIndex, field_children.name, $event)"
												:placeholder="field_children.placeholder"
												:close-on-select="false"
												:searchable="true"
												:object="true"
												:disabled="field_children.disabled"
												:options="selectOptionDepartments"
												:can-clear="field_children.validation ? false : true"
											>
												<template v-slot:option="{ option }">
													<div class="custom-option">
														<div class="option-label mb-1">
															{{ option.label }}
														</div>
														<div class="option-description text-secondary">
															<small>
																<i>{{ option.type }}</i>
															</small>
														</div>
													</div>
												</template>
											</Multiselect>
										</Field>
										<ErrorMessage
											as="div"
											:name="`itemChildrens[${itemIndex}].${field_children.name}`"
											class="text-danger"
										/>
									</div>
									<div v-else-if="field_children.type === 'FILEUPLOAD'">
										<Field
											:name="`itemChildrens[${itemIndex}].${field_children.name}`"
											:model-value="itemChildren[field_children.name]"
										>
											<FilePond
												:files="itemChildren[field_children.name]"
												@updatefiles="(fileItemUploads) => updateFileChildrens(fileItemUploads, itemChildren, field_children.name)"
												className="file-pond-children"
												:labelIdle="$t('validate_field.file_upload.label_idle_children')"
												:allowMultiple="field_children.multiple"
												:maxFiles="maxFiles"
												:maxFileSize="maxFileSize"
												:acceptedFileTypes="acceptedFileTypes"
												:labelFileTypeNotAllowed="$t('validate_field.file_upload.label_allowed')"
												:labelMaxFileSizeExceeded="$t('validate_field.file_upload.label_max_file_size_exceeded')"
												:fileValidateTypeLabelExpectedTypes="`${$t('validate_field.file_upload.label_expected_types')}`"
												:labelMaxFileSize="`${$t('validate_field.file_upload.label_max_file_size')} {filesize}`"
												:instantUpload="false"
												:name="`itemChildrens[${itemIndex}].${field_children.name}`"
												:ref="`itemChildrens[${itemIndex}].${field_children.name}`"
												credits="false"
												allow-reorder="true"
												item-insert-location="after"
												image-preview-min-height="60"
												image-preview-max-height="60"
											/>
										</Field>
										<ErrorMessage
											as="div"
											:name="`itemChildrens[${itemIndex}].${field_children.name}`"
											class="text-danger"
										/>
									</div>
									<div v-else-if="field_children.type === 'FORMULA'">
										<FormKit
											type="text"
											:name="`itemChildrens[${itemIndex}].${field_children.name}`"
											:model-value="itemChildren[field_children.name]"
											disabled="false"
										/>
									</div>
									<div v-else-if="field_children.type === 'OBJECTSYSTEM'">
										<div class="d-flex gap-2">
											<div :class="[`col`, `column-width-td`]">
												<Field
													:name="`itemChildrens[${itemIndex}].${field_children.name}`"
													v-slot="{ field }"
												>
													<Multiselect
														v-bind="field"
														:mode="field_children.multiple ? 'tags' : 'single'"
														:model-value="itemChildren[field_children.name]"
														@update:model-value="updateItemChildrens(fieldItem.name, itemIndex, field_children.name, $event)"
														:placeholder="field_children.placeholder"
														:close-on-select="false"
														:filter-results="false"
														:resolve-on-load="false"
														:infinite="true"
														:limit="10"
														:clear-on-search="true"
														:searchable="true"
														:delay="0"
														:min-chars="0"
														:object="true"
														:disabled="field_children.disabled"
														:options="async (query) => {
															return await getOptionColumnData(query, field_children)
														}"
														:can-clear="field_children.validation ? false : true"
														@change="showSubColumnTableChildren($event, itemIndex, field_children.name)"
														@open="getOptionColumnData('', field_children)"
													/>
												</Field>
												<ErrorMessage
													as="div"
													:name="`itemChildrens[${itemIndex}].${field_children.name}`"
													class="text-danger"
												/>
											</div>
											<div class="d-flex gap-2" v-if="subColumnTableDescriptionChildren[field_children.name] && subColumnTableDescriptionChildren[field_children.name][itemIndex]">
												<div :class="[`col`, `column-width-td`]" class="disabled-column-table-description" v-for="(subColumnDescriptionChildren, keyNameChildrenSubColumnTable) in subColumnTableDescriptionChildren[field_children.name][itemIndex]" :key="keyNameChildrenSubColumnTable">
													<FormKit
														type="text"
														:label="subColumnDescriptionChildren"
														:floating-label="true"
														:model-value="subColumnTableOptionSelectedChildren[field_children.name][itemIndex][keyNameChildrenSubColumnTable]"
													/>
												</div>
											</div>
										</div>
									</div>
									<div v-else>
										<FormKit
											:type="field_children.type"
											:name="`itemChildrens[${itemIndex}].${field_children.name}`"
											:validation="field_children.validation"
											:validation-messages="field_children.validationMessages"
											:class="field_children.class"
											:placeholder="field_children.placeholder"
											:model-value="itemChildren[field_children.name]"
											@update:model-value="updateItemChildrens(fieldItem.name, itemIndex, field_children.name, $event)"
											:disabled="field_children.disabled"
										/>
									</div>
								</td>
								<td class="col-md-1">
									<svg
										xmlns="http://www.w3.org/2000/svg"
										height="24px"
										viewBox="0 -960 960 960"
										width="24px"
										fill="#83868C"
										class="cursor-pointer mt-2"
										@click="removeItem(fieldItem, itemIndex)"
										v-if="itemIndex !== 0"
									>
										<path d="m256-200-56-56 224-224-224-224 56-56 224 224 224-224 56 56-224 224 224 224-56 56-224-224-224 224Z"/>
									</svg>
								</td>
							</tr>
						</tbody>
					</table>
					<button
						type="button"
						class="btn btn-primary ms-2 d-flex align-items-center"
						@click="addItem(fieldItem)"
					>
						<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFFFFF">
							<path d="M440-280h80v-160h160v-80H520v-160h-80v160H280v80h160v160Zm40 200q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z"/>
						</svg>
					</button>
				</div>
				<div v-else-if="fieldItem.type === 'OBJECTSYSTEM'">
					<div class="d-flex gap-2">
						<div :class="[`col`, `column-width-td`]">
							<label class="mb-1">
								{{ fieldItem.label }}
								<span v-if="fieldItem.validation" class="text-danger">*</span>
							</label>
							<Field
								:name="fieldItem.name"
								v-slot="{ field }"
							>
								<Multiselect
									v-bind="field"
									:mode="fieldItem.multiple ? 'tags' : 'single'"
									:value="fieldItem.value"
									:model-value="formData[fieldItem.name]"
									@update:model-value="updateFormData(fieldItem.name, $event)"
									:placeholder="fieldItem.placeholder"
									:close-on-select="false"
									:filter-results="false"
									:resolve-on-load="false"
									:infinite="true"
									:limit="10"
									:clear-on-search="true"
									:searchable="true"
									:delay="0"
									:min-chars="0"
									:object="true"
									:disabled="fieldItem.disabled"
									:options="async (query) => {
										return await getOptionColumnData(query, fieldItem)
									}"
									:can-clear="fieldItem.validation ? false : true"
									@change="showSubColumnTable($event, fieldItem.name)"
									@open="getOptionColumnData('', fieldItem)"
								/>
							</Field>
							<ErrorMessage
								as="div"
								:name="fieldItem.name"
								class="text-danger"
							/>
						</div>
						<div :class="[`col`, `column-width-td`]" class="disabled-column-table-description" v-for="(subColumnDescription, keyNameSubColumnTable) in subColumnTableDescription[fieldItem.name]" :key="keyNameSubColumnTable">
							<label></label>
							<FormKit
								type="text"
								:label="subColumnDescription"
								:floating-label="true"
								:model-value="subColumnTableOptionSelected[fieldItem.name][keyNameSubColumnTable]"
							/>
						</div>
					</div>
				</div>
				<div v-else>
					<FormKit
						v-bind="fieldItem"
						:model-value="formData[fieldItem.name]"
						@update:model-value="updateFormData(fieldItem.name, $event)"
					/>
				</div>
			</CCol>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import { useI18n } from "vue-i18n";
import Multiselect from '@vueform/multiselect';
import vueFilePond from 'vue-filepond';
import 'filepond/dist/filepond.min.css';
import 'filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css';
import FilePondPluginImagePreview from 'filepond-plugin-image-preview';
import FilePondPluginFileValidateType from 'filepond-plugin-file-validate-type';
import FilePondPluginFileValidateSize from 'filepond-plugin-file-validate-size';
import { Form, Field, ErrorMessage } from 'vee-validate';
import { FormKit } from '@formkit/vue';
import { CCol } from '@coreui/vue';

const FilePond: any = vueFilePond(FilePondPluginImagePreview, FilePondPluginFileValidateType, FilePondPluginFileValidateSize);

export default defineComponent({
	name: "DynamicFormFields",

	components: {
		Multiselect,
		FilePond,
		Form,
		Field,
		ErrorMessage,
		FormKit,
		CCol,
	},

	props: {
		formFields: {
			type: Array<any>,
			required: true
		},
		formData: {
			type: Object,
			required: true
		},
		itemChildrens: {
			type: Object,
			required: true
		},
		selectOptionDepartments: {
			type: Array,
			required: true
		},
		subColumnTableDescription: {
			type: Object,
			required: true
		},
		subColumnTableOptionSelected: {
			type: Object,
			required: true
		},
		subColumnTableDescriptionChildren: {
			type: Object,
			required: true
		},
		subColumnTableOptionSelectedChildren: {
			type: Object,
			required: true
		},
		formattedFormulaResults: {
			type: Object,
			required: true
		},
		maxFiles: {
			type: Number,
			required: true
		},
		maxFileSize: {
			type: String,
			required: true
		},
		acceptedFileTypes: {
			type: Array,
			required: true
		},
		getOptionUsers: {
			type: Function,
			required: true
		},
		getOptionColumnData: {
			type: Function,
			required: true
		},
		formattedFormulaChildrenResults: {
			type: Function,
			required: true
		},
		converFormFields: {
			type: Function,
			required: true
		}
	},

	emits: [
		'update-form-data',
		'update-item-childrens',
		'update-files',
		'update-file-childrens',
		'add-item',
		'remove-item',
		'show-sub-column-table',
		'show-sub-column-table-children'
	],

	setup(props, { emit }) {
		const { t } = useI18n();

		const updateFormData = (fieldName: string, value: any) => {
			emit('update-form-data', fieldName, value);
		};

		const updateItemChildrens = (tableName: string, itemIndex: number, fieldName: string, value: any) => {
			// Update the specific field in itemChildrens
			const updatedItemChildrens = { ...props.itemChildrens };
			if (updatedItemChildrens[tableName] && updatedItemChildrens[tableName][itemIndex]) {
				updatedItemChildrens[tableName][itemIndex][fieldName] = value;
			}
			emit('update-item-childrens', updatedItemChildrens);
		};

		const updateFiles = (fileItemUploads: any, fieldName: string) => {
			emit('update-files', fileItemUploads, fieldName);
		};

		const updateFileChildrens = (fileItemUploads: any, itemChildren: object, fieldChildrenName: string) => {
			emit('update-file-childrens', fileItemUploads, itemChildren, fieldChildrenName);
		};

		const addItem = (field: any) => {
			emit('add-item', field);
		};

		const removeItem = (field: any, itemIndex: number) => {
			emit('remove-item', field, itemIndex);
		};

		const showSubColumnTable = (optionSelected: any, keyName: string) => {
			emit('show-sub-column-table', optionSelected, keyName);
		};

		const showSubColumnTableChildren = (optionSelected: any, itemIndex: number, keyName: string) => {
			emit('show-sub-column-table-children', optionSelected, itemIndex, keyName);
		};

		return {
			t,
			updateFormData,
			updateItemChildrens,
			updateFiles,
			updateFileChildrens,
			addItem,
			removeItem,
			showSubColumnTable,
			showSubColumnTableChildren,
			getOptionUsers: props.getOptionUsers,
			getOptionColumnData: props.getOptionColumnData,
			formattedFormulaChildrenResults: props.formattedFormulaChildrenResults,
			converFormFields: props.converFormFields,
		}
	}
});
</script>

<style scoped>
.cursor-pointer {
	cursor: pointer;
}
.column-width-td {
	min-width: 200px !important;
}
.disabled-column-table-description {
	pointer-events: none;
}
</style>
<style src="@vueform/multiselect/themes/default.css"></style>
